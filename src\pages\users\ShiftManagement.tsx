import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Plus,
  Calendar,
  Clock,
  Users,
  Play,
  Square,
  Edit,
  Trash2,
  Eye,
  MapPin,
  Monitor,
  AlertCircle,
  CheckCircle,
  UserCheck,
  Timer,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

// Mock data for shifts
const mockShifts = [
  {
    id: 1,
    employeeId: 1,
    employeeName: "John Doe",
    employeeNumber: "EMP001",
    role: "Waiter",
    branch: "Nairobi Branch",
    revenueCenter: "Restaurant",
    workstation: "Table Service A",
    startTime: "08:00",
    endTime: "16:00",
    date: "2024-01-15",
    status: "Active",
    clockInTime: "07:58",
    clockOutTime: null,
    duration: "8h 2m",
    isLate: false,
    isEarlyExit: false,
    avatar: "/api/placeholder/40/40",
  },
  {
    id: 2,
    employeeId: 2,
    employeeName: "Jane Smith",
    employeeNumber: "EMP002",
    role: "Chef",
    branch: "Mombasa Branch",
    revenueCenter: "Kitchen",
    workstation: "Grill Station",
    startTime: "06:00",
    endTime: "14:00",
    date: "2024-01-15",
    status: "Completed",
    clockInTime: "06:15",
    clockOutTime: "14:05",
    duration: "7h 50m",
    isLate: true,
    isEarlyExit: false,
    avatar: "/api/placeholder/40/40",
  },
  {
    id: 3,
    employeeId: 3,
    employeeName: "Mike Johnson",
    employeeNumber: "EMP003",
    role: "Cashier",
    branch: "Kisumu Branch",
    revenueCenter: "Front Desk",
    workstation: "POS Terminal 1",
    startTime: "09:00",
    endTime: "17:00",
    date: "2024-01-15",
    status: "Scheduled",
    clockInTime: null,
    clockOutTime: null,
    duration: "0h 0m",
    isLate: false,
    isEarlyExit: false,
    avatar: "/api/placeholder/40/40",
  },
  {
    id: 4,
    employeeId: 4,
    employeeName: "Sarah Wilson",
    employeeNumber: "EMP004",
    role: "Manager",
    branch: "Nairobi Branch",
    revenueCenter: "Administration",
    workstation: "Management Office",
    startTime: "10:00",
    endTime: "18:00",
    date: "2024-01-15",
    status: "Completed",
    clockInTime: "09:45",
    clockOutTime: "17:30",
    duration: "7h 45m",
    isLate: false,
    isEarlyExit: true,
    avatar: "/api/placeholder/40/40",
  },
];

// Mock data for employees
const mockEmployees = [
  { id: 1, name: "John Doe", number: "EMP001", role: "Waiter" },
  { id: 2, name: "Jane Smith", number: "EMP002", role: "Chef" },
  { id: 3, name: "Mike Johnson", number: "EMP003", role: "Cashier" },
  { id: 4, name: "Sarah Wilson", number: "EMP004", role: "Manager" },
];

const branches = ["Nairobi Branch", "Mombasa Branch", "Kisumu Branch", "Nakuru Branch"];
const revenueCenters = ["Restaurant", "Kitchen", "Front Desk", "Administration", "Bar"];
const workstations = ["Table Service A", "Grill Station", "POS Terminal 1", "Management Office", "Bar Counter"];

export default function ShiftManagement() {
  const [activeTab, setActiveTab] = useState("calendar");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newShift, setNewShift] = useState({
    employeeId: "",
    date: "",
    startTime: "",
    endTime: "",
    branch: "",
    revenueCenter: "",
    workstation: "",
    isGuestShift: false,
  });
  const navigate = useNavigate();

  const handleCreateShift = () => {
    // Handle shift creation logic here
    console.log("Creating shift:", newShift);
    setIsCreateModalOpen(false);
    setNewShift({
      employeeId: "",
      date: "",
      startTime: "",
      endTime: "",
      branch: "",
      revenueCenter: "",
      workstation: "",
      isGuestShift: false,
    });
  };

  const handleStartShift = (shiftId: number) => {
    // Handle start shift logic here
    console.log("Starting shift:", shiftId);
  };

  const handleEndShift = (shiftId: number) => {
    // Handle end shift logic here
    console.log("Ending shift:", shiftId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Completed":
        return "bg-blue-100 text-blue-800";
      case "Scheduled":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const activeShifts = mockShifts.filter(shift => shift.status === "Active").length;
  const completedShifts = mockShifts.filter(shift => shift.status === "Completed").length;
  const scheduledShifts = mockShifts.filter(shift => shift.status === "Scheduled").length;
  const lateEntries = mockShifts.filter(shift => shift.isLate).length;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/admin/users">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Shift Management</h1>
            <p className="text-muted-foreground">
              Manage employee shifts, workstations, and time tracking
            </p>
          </div>
        </div>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Shift
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Shift</DialogTitle>
              <DialogDescription>
                Schedule a new shift for an employee with specific time and workstation.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="employee">Employee *</Label>
                <Select
                  value={newShift.employeeId}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, employeeId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockEmployees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id.toString()}>
                        {employee.name} ({employee.number}) - {employee.role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date *</Label>
                  <Input
                    id="date"
                    type="date"
                    value={newShift.date}
                    onChange={(e) => setNewShift(prev => ({ ...prev, date: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startTime">Start Time *</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={newShift.startTime}
                    onChange={(e) => setNewShift(prev => ({ ...prev, startTime: e.target.value }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={newShift.endTime}
                  onChange={(e) => setNewShift(prev => ({ ...prev, endTime: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="branch">Branch *</Label>
                  <Select
                    value={newShift.branch}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, branch: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch} value={branch}>
                          {branch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="revenueCenter">Revenue Center *</Label>
                  <Select
                    value={newShift.revenueCenter}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, revenueCenter: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select revenue center" />
                    </SelectTrigger>
                    <SelectContent>
                      {revenueCenters.map((center) => (
                        <SelectItem key={center} value={center}>
                          {center}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="workstation">Workstation *</Label>
                <Select
                  value={newShift.workstation}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, workstation: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select workstation" />
                  </SelectTrigger>
                  <SelectContent>
                    {workstations.map((station) => (
                      <SelectItem key={station} value={station}>
                        {station}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isGuestShift"
                  checked={newShift.isGuestShift}
                  onCheckedChange={(checked) => setNewShift(prev => ({ ...prev, isGuestShift: checked as boolean }))}
                />
                <Label htmlFor="isGuestShift">Guest Shift Override</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateShift}>Create Shift</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shifts</CardTitle>
            <Play className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeShifts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{completedShifts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{scheduledShifts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Late Entries</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{lateEntries}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="employees">Employee List</TabsTrigger>
          <TabsTrigger value="branches">Branch View</TabsTrigger>
          <TabsTrigger value="logs">Clock Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="calendar" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Today's Shifts - {new Date().toLocaleDateString()}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Workstation</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead>Clock In/Out</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockShifts.map((shift) => (
                    <TableRow key={shift.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage src={shift.avatar} />
                            <AvatarFallback>
                              {shift.employeeName.split(" ").map(n => n[0]).join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{shift.employeeName}</div>
                            <div className="text-sm text-muted-foreground">
                              {shift.employeeNumber} - {shift.role}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{shift.workstation}</div>
                          <div className="text-sm text-muted-foreground">
                            {shift.branch} - {shift.revenueCenter}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {shift.startTime} - {shift.endTime}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center space-x-1">
                            <span>In: {shift.clockInTime || "Not clocked"}</span>
                            {shift.isLate && (
                              <AlertCircle className="h-3 w-3 text-red-500" />
                            )}
                          </div>
                          <div className="flex items-center space-x-1">
                            <span>Out: {shift.clockOutTime || "Not clocked"}</span>
                            {shift.isEarlyExit && (
                              <AlertCircle className="h-3 w-3 text-orange-500" />
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">{shift.duration}</div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(shift.status)}>
                          {shift.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {shift.status === "Scheduled" && (
                            <Button
                              size="sm"
                              onClick={() => handleStartShift(shift.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <Play className="h-3 w-3 mr-1" />
                              Start
                            </Button>
                          )}
                          {shift.status === "Active" && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleEndShift(shift.id)}
                            >
                              <Square className="h-3 w-3 mr-1" />
                              End
                            </Button>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Shift
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <UserCheck className="mr-2 h-4 w-4" />
                                Override Clock
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Shift
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shifts by Employee</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockEmployees.map((employee) => {
                  const employeeShifts = mockShifts.filter(shift => shift.employeeId === employee.id);
                  return (
                    <div key={employee.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback>
                              {employee.name.split(" ").map(n => n[0]).join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{employee.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {employee.number} - {employee.role}
                            </div>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {employeeShifts.length} shifts
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {employeeShifts.map((shift) => (
                          <div key={shift.id} className="text-sm p-2 bg-gray-50 rounded">
                            <div className="font-medium">{shift.startTime} - {shift.endTime}</div>
                            <div className="text-muted-foreground">{shift.workstation}</div>
                            <Badge size="sm" className={getStatusColor(shift.status)}>
                              {shift.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shifts by Branch</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {branches.map((branch) => {
                  const branchShifts = mockShifts.filter(shift => shift.branch === branch);
                  return (
                    <div key={branch} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <MapPin className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{branch}</div>
                            <div className="text-sm text-muted-foreground">
                              {branchShifts.length} shifts scheduled
                            </div>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {branchShifts.filter(s => s.status === "Active").length} active
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {branchShifts.map((shift) => (
                          <div key={shift.id} className="text-sm p-2 bg-gray-50 rounded">
                            <div className="font-medium">{shift.employeeName}</div>
                            <div className="text-muted-foreground">
                              {shift.workstation} ({shift.startTime} - {shift.endTime})
                            </div>
                            <Badge size="sm" className={getStatusColor(shift.status)}>
                              {shift.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Clock In/Out Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Scheduled</TableHead>
                    <TableHead>Actual</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockShifts.filter(shift => shift.clockInTime).map((shift) => (
                    <TableRow key={shift.id}>
                      <TableCell>
                        <div className="font-medium">{shift.employeeName}</div>
                        <div className="text-sm text-muted-foreground">
                          {shift.employeeNumber}
                        </div>
                      </TableCell>
                      <TableCell>{shift.date}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {shift.startTime} - {shift.endTime}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {shift.clockInTime} - {shift.clockOutTime || "In progress"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{shift.duration}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {shift.isLate && (
                            <Badge variant="destructive" className="text-xs">
                              Late Entry
                            </Badge>
                          )}
                          {shift.isEarlyExit && (
                            <Badge variant="secondary" className="text-xs">
                              Early Exit
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}