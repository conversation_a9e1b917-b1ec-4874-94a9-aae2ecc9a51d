import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Printer,
  Building2,
  Store,
  Wifi,
  WifiOff,
  TestTube,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Printer as PrinterType, PrinterType as PrinterTypeEnum, PrinterInterfaceType } from '@/types/pos';

// Mock data
const mockPrinters: PrinterType[] = [
  {
    id: 'printer1',
    name: 'Kitchen Printer 1',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc1',
    revenueCenter: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    ipAddress: '*************',
    deviceName: 'EPSON-TM-T88V',
    interfaceType: PrinterInterfaceType.LAN,
    type: PrinterTypeEnum.KITCHEN,
    workstationIds: ['ws1', 'ws2'],
    menuItemRouting: ['pizza', 'pasta', 'salads'],
    categoryRouting: ['main-course', 'appetizers'],
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'printer2',
    name: 'Receipt Printer 1',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc1',
    revenueCenter: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    ipAddress: '*************',
    deviceName: 'STAR-TSP143',
    interfaceType: PrinterInterfaceType.LAN,
    type: PrinterTypeEnum.RECEIPT,
    workstationIds: ['ws1'],
    menuItemRouting: [],
    categoryRouting: [],
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'printer3',
    name: 'Bar Printer',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc2',
    revenueCenter: { id: 'rc2', code: 'RC002', name: 'Bar & Lounge', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    deviceName: 'BAR-PRINTER-BT',
    interfaceType: PrinterInterfaceType.BLUETOOTH,
    type: PrinterTypeEnum.KITCHEN,
    workstationIds: ['ws3'],
    menuItemRouting: ['cocktails', 'beer', 'wine'],
    categoryRouting: ['beverages'],
    isActive: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

const PrinterManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [selectedType, setSelectedType] = useState('all');
  const [printers] = useState<PrinterType[]>(mockPrinters);

  const columns: ColumnDef<PrinterType>[] = [
    {
      accessorKey: 'name',
      header: 'Printer Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Printer className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.deviceName}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('type') as PrinterTypeEnum;
        const getTypeColor = (type: PrinterTypeEnum) => {
          switch (type) {
            case PrinterTypeEnum.KITCHEN: return 'bg-orange-100 text-orange-800';
            case PrinterTypeEnum.RECEIPT: return 'bg-blue-100 text-blue-800';
            case PrinterTypeEnum.BILL: return 'bg-green-100 text-green-800';
            case PrinterTypeEnum.BACKUP: return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };
        
        return (
          <Badge variant="outline" className={getTypeColor(type)}>
            {type}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.original.branch;
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name}</div>
              <div className="text-sm text-muted-foreground">{branch?.code}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'revenueCenter',
      header: 'Revenue Center',
      cell: ({ row }) => {
        const rc = row.original.revenueCenter;
        return rc ? (
          <div className="flex items-center space-x-2">
            <Store className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{rc.name}</div>
              <div className="text-sm text-muted-foreground">{rc.code}</div>
            </div>
          </div>
        ) : (
          <span className="text-muted-foreground">Not assigned</span>
        );
      },
    },
    {
      accessorKey: 'interfaceType',
      header: 'Interface',
      cell: ({ row }) => {
        const interfaceType = row.getValue('interfaceType') as PrinterInterfaceType;
        const ipAddress = row.original.ipAddress;
        const isActive = row.original.isActive;
        
        return (
          <div className="flex items-center space-x-2">
            {isActive ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <div>
              <div className="font-medium">{interfaceType}</div>
              {ipAddress && (
                <div className="text-sm text-muted-foreground font-mono">{ipAddress}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'workstationIds',
      header: 'Workstations',
      cell: ({ row }) => {
        const workstationIds = row.getValue('workstationIds') as string[];
        return (
          <div className="text-sm">
            {workstationIds.length} connected
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Online' : 'Offline'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const printer = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/printers/${printer.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Configuration
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/printers/${printer.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Printer
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <TestTube className="h-4 w-4 mr-2" />
                Test Print
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Configure Routing
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {printer.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Printer
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredPrinters = printers.filter(printer => {
    const matchesSearch = printer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         printer.deviceName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         printer.ipAddress?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBranch = selectedBranch === 'all' || printer.branchId === selectedBranch;
    const matchesType = selectedType === 'all' || printer.type === selectedType;
    return matchesSearch && matchesBranch && matchesType;
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Printer Management</h1>
          <p className="text-muted-foreground">
            Manage thermal and receipt printers for workstations and kitchen displays
          </p>
        </div>
        <Link to="/pos/printers/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Printer
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find printers by name, device, or network configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search printers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                <SelectItem value="1">Downtown Hotel</SelectItem>
                <SelectItem value="2">Airport Branch</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.values(PrinterTypeEnum).map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Printers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Printers ({filteredPrinters.length})</CardTitle>
          <CardDescription>
            Thermal and receipt printers across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredPrinters}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{printers.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {printers.filter(p => p.isActive).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Kitchen Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {printers.filter(p => p.type === PrinterTypeEnum.KITCHEN).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Receipt Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {printers.filter(p => p.type === PrinterTypeEnum.RECEIPT).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">LAN Connected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {printers.filter(p => p.interfaceType === PrinterInterfaceType.LAN).length}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PrinterManagement;
