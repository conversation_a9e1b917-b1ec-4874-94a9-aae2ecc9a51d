import { useState, useEffect } from 'react';
import { Branch, RevenueCenter, Workstation, TaxClass, Printer } from '@/types/pos';

// Mock data hooks for POS configuration
// In a real application, these would connect to your API

export const useBranches = () => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate API call
    const fetchBranches = async () => {
      try {
        setLoading(true);
        // Mock delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data - replace with actual API call
        const mockBranches: Branch[] = [
          {
            id: '1',
            code: 'BR001',
            name: 'Downtown Hotel',
            physicalLocation: 'Nairobi CBD',
            address: '123 Kenyatta Avenue, Nairobi',
            timezone: 'Africa/Nairobi',
            currency: 'KES',
            taxConfiguration: ['tax-class-1', 'tax-class-2'],
            isActive: true,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          }
        ];
        
        setBranches(mockBranches);
      } catch (err) {
        setError('Failed to fetch branches');
      } finally {
        setLoading(false);
      }
    };

    fetchBranches();
  }, []);

  return { branches, loading, error, setBranches };
};

export const useRevenueCenters = (branchId?: string) => {
  const [revenueCenters, setRevenueCenters] = useState<RevenueCenter[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRevenueCenters = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock data
        const mockRevenueCenters: RevenueCenter[] = [
          {
            id: '1',
            code: 'RC001',
            name: 'Main Restaurant',
            description: 'Primary dining area',
            branchId: '1',
            taxClassId: 'tax-class-1',
            salesCategories: ['food', 'beverages'],
            isActive: true,
            serviceChargeApplies: true,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          }
        ];
        
        const filtered = branchId 
          ? mockRevenueCenters.filter(rc => rc.branchId === branchId)
          : mockRevenueCenters;
          
        setRevenueCenters(filtered);
      } catch (err) {
        setError('Failed to fetch revenue centers');
      } finally {
        setLoading(false);
      }
    };

    fetchRevenueCenters();
  }, [branchId]);

  return { revenueCenters, loading, error, setRevenueCenters };
};

export const useWorkstations = (branchId?: string, revenueCenterId?: string) => {
  const [workstations, setWorkstations] = useState<Workstation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWorkstations = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 600));
        
        // Mock data
        const mockWorkstations: Workstation[] = [
          {
            id: '1',
            name: 'Main Counter POS',
            branchId: '1',
            revenueCenterId: 'rc1',
            type: 'Full POS (Orders + Payments)' as any,
            printerIds: ['printer1', 'printer2'],
            ipAddress: '*************',
            touchscreenCompatible: true,
            isActive: true,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          }
        ];
        
        let filtered = mockWorkstations;
        if (branchId) {
          filtered = filtered.filter(ws => ws.branchId === branchId);
        }
        if (revenueCenterId) {
          filtered = filtered.filter(ws => ws.revenueCenterId === revenueCenterId);
        }
        
        setWorkstations(filtered);
      } catch (err) {
        setError('Failed to fetch workstations');
      } finally {
        setLoading(false);
      }
    };

    fetchWorkstations();
  }, [branchId, revenueCenterId]);

  return { workstations, loading, error, setWorkstations };
};

export const useTaxClasses = () => {
  const [taxClasses, setTaxClasses] = useState<TaxClass[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTaxClasses = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock data
        const mockTaxClasses: TaxClass[] = [
          {
            id: 'tax-class-1',
            name: 'Food Tax',
            description: 'Standard tax rate for food items',
            applicableRevenueCenters: ['rc1'],
            taxRates: [
              {
                id: 'rate-1',
                name: 'VAT 16%',
                percentage: 16,
                taxClassId: 'tax-class-1',
                isInclusive: false,
                isActive: true,
                createdAt: '2024-01-15T10:00:00Z',
                updatedAt: '2024-01-15T10:00:00Z'
              }
            ],
            isActive: true,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          }
        ];
        
        setTaxClasses(mockTaxClasses);
      } catch (err) {
        setError('Failed to fetch tax classes');
      } finally {
        setLoading(false);
      }
    };

    fetchTaxClasses();
  }, []);

  return { taxClasses, loading, error, setTaxClasses };
};

export const usePrinters = (branchId?: string, revenueCenterId?: string) => {
  const [printers, setPrinters] = useState<Printer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrinters = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 700));
        
        // Mock data
        const mockPrinters: Printer[] = [
          {
            id: 'printer1',
            name: 'Kitchen Printer 1',
            branchId: '1',
            revenueCenterId: 'rc1',
            ipAddress: '*************',
            deviceName: 'EPSON-TM-T88V',
            interfaceType: 'LAN' as any,
            type: 'Kitchen' as any,
            workstationIds: ['ws1', 'ws2'],
            menuItemRouting: ['pizza', 'pasta'],
            categoryRouting: ['main-course'],
            isActive: true,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          }
        ];
        
        let filtered = mockPrinters;
        if (branchId) {
          filtered = filtered.filter(p => p.branchId === branchId);
        }
        if (revenueCenterId) {
          filtered = filtered.filter(p => p.revenueCenterId === revenueCenterId);
        }
        
        setPrinters(filtered);
      } catch (err) {
        setError('Failed to fetch printers');
      } finally {
        setLoading(false);
      }
    };

    fetchPrinters();
  }, [branchId, revenueCenterId]);

  return { printers, loading, error, setPrinters };
};

// Generic hook for CRUD operations
export const usePOSCrud = <T extends { id: string }>(entityType: string) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const create = async (data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newEntity = {
        ...data,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as T;
      
      return newEntity;
    } catch (err) {
      const errorMessage = `Failed to create ${entityType}`;
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const update = async (id: string, data: Partial<T>): Promise<T> => {
    setLoading(true);
    setError(null);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedEntity = {
        ...data,
        id,
        updatedAt: new Date().toISOString(),
      } as T;
      
      return updatedEntity;
    } catch (err) {
      const errorMessage = `Failed to update ${entityType}`;
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const remove = async (id: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      // Simulate successful deletion
    } catch (err) {
      const errorMessage = `Failed to delete ${entityType}`;
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { create, update, remove, loading, error };
};
