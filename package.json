{"name": "gmc-pos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.3.0", "@tanstack/react-query": "^5.64.2", "@tanstack/react-table": "^8.21.3", "@tippyjs/react": "^4.2.6", "@xyflow/react": "^12.4.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.10.0", "gmc-pos": "file:", "idle-js": "^1.2.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.471.1", "next-themes": "^0.4.4", "pdfmake": "^0.2.18", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.54.2", "react-modal": "^3.16.3", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.1", "react-simple-maps": "^3.0.0", "react-spinners": "^0.15.0", "react-tailwindcss-select": "^1.8.5", "react-time-picker": "^7.0.0", "react-tooltip": "^5.28.1", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/idle-js": "^1.2.3", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.10.6", "@types/pdfkit": "^0.14.0", "@types/pdfmake": "^0.2.11", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-modal": "^3.16.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}