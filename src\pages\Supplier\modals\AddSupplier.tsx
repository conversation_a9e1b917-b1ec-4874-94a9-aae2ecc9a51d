import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAddSuppliersMutation } from "@/redux/slices/suppliers";
import { supplierType } from "@/types/suppliers";
import { Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: supplierType;
}
const AddSupplier = ({ isOpen, onClose, updateData }: propTypes) => {
  const [createSupplier, { isLoading: loading }] = useAddSuppliersMutation();

  const [formData, setformData] = useState<supplierType>({
    name: "",
    category: "",
    branch: "",
    code: "",
    contact_name: "",
    email: "",
    phone_number: "",
    alt_phone_number: "",
    address_physical: "",
    address_mailing: "",
    country: "",
    city_or_state: "",
    tax_vat_16: false,
    tax_exempt: false,
    payment_terms: "",
    status: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setformData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setformData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleAddSupplier = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const res: any = await createSupplier(formData).unwrap();
      if (res?.success) {
        setformData({
          name: "",
          category: "",
          branch: "",
          code: "",
          contact_name: "",
          email: "",
          phone_number: "",
          alt_phone_number: "",
          address_physical: "",
          address_mailing: "",
          country: "",
          city_or_state: "",
          tax_vat_16: false,
          tax_exempt: false,
          payment_terms: "",
          status: "",
        });
        onClose();
        toast.success("Supplier added successfully");
      }
    } catch (error: any) {
      let errorMsg;
      if (error?.data) {
        errorMsg = error.data?.message;
      } else {
        errorMsg = "Failed to add supplier. Please try again.";
      }
      toast.error(
        typeof errorMsg === "string" ? errorMsg : JSON.stringify(errorMsg)
      );
      console.error("Failed to add supplier:", error);
    }
  };

  return (
    <BaseModal
      size="xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add Prospect"
      description="Complete all steps to add a new prospect"
      // onStepChange={setCurrentStep}
      // onComplete={handleAddProspect}
    >
      <form onSubmit={handleAddSupplier}>
        <div>
          <div className="space-y-4 py-2">
            {/* Supplier Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Supplier Name*</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter supplier name"
              />
            </div>

            {/* Code (auto-generated or optional input) */}
            <div className="space-y-2">
              <Label htmlFor="code">Supplier Code</Label>
              <Input
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="Auto-generated or enter manually"
              />
            </div>

            {/* Category & Branch */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category*</Label>
                <Input
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  placeholder="Enter category"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="branch">Branch</Label>
                <Input
                  id="branch"
                  name="branch"
                  value={formData.branch}
                  onChange={handleInputChange}
                  placeholder="Enter branch"
                />
              </div>
            </div>

            {/* Contact Info */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact_name">Contact Name*</Label>
                <Input
                  id="contact_name"
                  name="contact_name"
                  value={formData.contact_name}
                  onChange={handleInputChange}
                  placeholder="Enter contact name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter email address"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone_number">Phone Number*</Label>
                <Input
                  id="phone_number"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  placeholder="Enter phone number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="alt_phone_number">Alt. Phone Number</Label>
                <Input
                  id="alt_phone_number"
                  name="alt_phone_number"
                  value={formData.alt_phone_number}
                  onChange={handleInputChange}
                  placeholder="Enter alternate phone number"
                />
              </div>
            </div>

            {/* Location Info */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country*</Label>
                <Input
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Enter country"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="city_or_state">City / State*</Label>
                <Input
                  id="city_or_state"
                  name="city_or_state"
                  value={formData.city_or_state}
                  onChange={handleInputChange}
                  placeholder="Enter city or state"
                />
              </div>
            </div>

            {/* Tax Fields */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="tax_vat_16"
                  name="tax_vat_16"
                  checked={formData.tax_vat_16}
                  onChange={handleCheckboxChange}
                />
                <Label htmlFor="tax_vat_16">Subject to VAT (16%)</Label>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="tax_exempt"
                  name="tax_exempt"
                  checked={formData.tax_exempt}
                  onChange={handleCheckboxChange}
                />
                <Label htmlFor="tax_exempt">Tax Exempt</Label>
              </div>
            </div>

            {/* Payment Terms */}
            <div className="space-y-2">
              <Label htmlFor="payment_terms">Payment Terms*</Label>
              <select
                id="payment_terms"
                name="payment_terms"
                value={formData.payment_terms}
                onChange={handleInputChange}
                className="w-full border rounded-md p-2"
              >
                <option value="">Select terms</option>
                <option value="Upon Delivery">Upon Delivery</option>
                <option value="15 Days">15 Days</option>
                <option value="30 Days">30 Days</option>
              </select>
            </div>

            {/* GL Account */}
            <div className="space-y-2">
              <Label htmlFor="gl_account">GL Account</Label>
              <Input
                id="gl_account"
                name="gl_account"
                value={formData.gl_account}
                onChange={handleInputChange}
                placeholder="Enter GL account"
              />
            </div>

            {/* Addresses */}
            <div className="space-y-2">
              <Label htmlFor="address_physical">Physical Address*</Label>
              <Textarea
                id="address_physical"
                name="address_physical"
                value={formData.address_physical}
                onChange={handleInputChange}
                placeholder="Enter physical address"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address_mailing">Mailing Address*</Label>
              <Textarea
                id="address_mailing"
                name="address_mailing"
                value={formData.address_mailing}
                onChange={handleInputChange}
                placeholder="Enter mailing address"
              />
            </div>
          </div>

          <div className="w-full flex justify-end my-3 ">
            {loading ? (
              <SpinnerTemp size="sm" type="spinner-double" />
            ) : (
              <Button type="submit" variant="default">
                Submit
                <Send />
              </Button>
            )}
          </div>
        </div>
      </form>
    </BaseModal>
  );
};

export default AddSupplier;
