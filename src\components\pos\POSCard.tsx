import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LucideIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

interface POSCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  iconColor?: string;
  stats?: {
    total: number;
    active: number;
    label?: string;
  };
  actions?: {
    primary?: {
      label: string;
      href: string;
    };
    secondary?: {
      label: string;
      href: string;
    };
  };
  badges?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  }[];
  className?: string;
}

const POSCard: React.FC<POSCardProps> = ({
  title,
  description,
  icon: Icon,
  iconColor = 'bg-blue-500',
  stats,
  actions,
  badges,
  className
}) => {
  return (
    <Card className={`hover:shadow-lg transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className={`p-2 rounded-lg ${iconColor} text-white`}>
            <Icon className="h-6 w-6" />
          </div>
          {actions?.secondary && (
            <Link to={actions.secondary.href}>
              <Button variant="ghost" size="sm">
                {actions.secondary.label}
              </Button>
            </Link>
          )}
        </div>
        <CardTitle className="text-lg">{title}</CardTitle>
        <CardDescription className="text-sm">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stats && (
          <div className="flex items-center justify-between text-sm mb-4">
            <span className="text-muted-foreground">
              Total: {stats.total}
            </span>
            <span className="text-green-600 font-medium">
              Active: {stats.active}
            </span>
          </div>
        )}
        
        {badges && badges.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {badges.map((badge, index) => (
              <Badge key={index} variant={badge.variant || 'outline'}>
                {badge.text}
              </Badge>
            ))}
          </div>
        )}
        
        {actions?.primary && (
          <Link to={actions.primary.href}>
            <Button className="w-full" variant="outline">
              {actions.primary.label}
            </Button>
          </Link>
        )}
      </CardContent>
    </Card>
  );
};

export default POSCard;
