import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Monitor,
  Building2,
  Store,
  Printer,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Workstation, WorkstationType } from '@/types/pos';

// Mock data
const mockWorkstations: Workstation[] = [
  {
    id: '1',
    name: 'Main Counter POS',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc1',
    revenueCenter: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    type: WorkstationType.FULL_POS,
    printerIds: ['printer1', 'printer2'],
    ipAddress: '*************',
    hostname: 'pos-main-01',
    touchscreenCompatible: true,
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Waiter Tablet 1',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc1',
    revenueCenter: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    type: WorkstationType.WAITER_TABLET,
    printerIds: ['printer1'],
    ipAddress: '*************',
    touchscreenCompatible: true,
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '3',
    name: 'Kitchen Display',
    branchId: '1',
    branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: '', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
    revenueCenterId: 'rc1',
    revenueCenter: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branchId: '1', salesCategories: [], isActive: true, serviceChargeApplies: false, createdAt: '', updatedAt: '' },
    type: WorkstationType.KITCHEN_DISPLAY,
    printerIds: [],
    ipAddress: '*************',
    touchscreenCompatible: true,
    isActive: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

const WorkstationManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [selectedType, setSelectedType] = useState('all');
  const [workstations] = useState<Workstation[]>(mockWorkstations);

  const columns: ColumnDef<Workstation>[] = [
    {
      accessorKey: 'name',
      header: 'Workstation Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Monitor className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.hostname}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('type') as WorkstationType;
        const getTypeColor = (type: WorkstationType) => {
          switch (type) {
            case WorkstationType.FULL_POS: return 'bg-blue-100 text-blue-800';
            case WorkstationType.WAITER_TABLET: return 'bg-green-100 text-green-800';
            case WorkstationType.KITCHEN_DISPLAY: return 'bg-orange-100 text-orange-800';
            case WorkstationType.ORDER_ONLY: return 'bg-purple-100 text-purple-800';
            case WorkstationType.SELF_SERVICE_KIOSK: return 'bg-pink-100 text-pink-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };
        
        return (
          <Badge variant="outline" className={getTypeColor(type)}>
            {type}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.original.branch;
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name}</div>
              <div className="text-sm text-muted-foreground">{branch?.code}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'revenueCenter',
      header: 'Revenue Center',
      cell: ({ row }) => {
        const rc = row.original.revenueCenter;
        return rc ? (
          <div className="flex items-center space-x-2">
            <Store className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{rc.name}</div>
              <div className="text-sm text-muted-foreground">{rc.code}</div>
            </div>
          </div>
        ) : (
          <span className="text-muted-foreground">Not assigned</span>
        );
      },
    },
    {
      accessorKey: 'ipAddress',
      header: 'IP Address',
      cell: ({ row }) => {
        const ipAddress = row.getValue('ipAddress') as string;
        const isActive = row.original.isActive;
        return (
          <div className="flex items-center space-x-2">
            {isActive ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="font-mono text-sm">{ipAddress}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'printerIds',
      header: 'Printers',
      cell: ({ row }) => {
        const printerIds = row.getValue('printerIds') as string[];
        return (
          <div className="flex items-center space-x-2">
            <Printer className="h-4 w-4 text-muted-foreground" />
            <span>{printerIds.length}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Online' : 'Offline'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const workstation = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Configuration
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Workstation
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Printer className="h-4 w-4 mr-2" />
                Manage Printers
              </DropdownMenuItem>
              <DropdownMenuItem>
                Reassign to Branch/RVC
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {workstation.isActive ? 'Take Offline' : 'Bring Online'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Workstation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredWorkstations = workstations.filter(ws => {
    const matchesSearch = ws.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.hostname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.ipAddress?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBranch = selectedBranch === 'all' || ws.branchId === selectedBranch;
    const matchesType = selectedType === 'all' || ws.type === selectedType;
    return matchesSearch && matchesBranch && matchesType;
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workstation Management</h1>
          <p className="text-muted-foreground">
            Manage POS devices, terminals, and kitchen displays
          </p>
        </div>
        <Link to="/pos/workstations/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Workstation
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find workstations by name, IP address, or configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workstations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                <SelectItem value="1">Downtown Hotel</SelectItem>
                <SelectItem value="2">Airport Branch</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.values(WorkstationType).map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Workstations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Workstations ({filteredWorkstations.length})</CardTitle>
          <CardDescription>
            POS devices and terminals across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredWorkstations}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Workstations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workstations.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {workstations.filter(ws => ws.isActive).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Full POS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.type === WorkstationType.FULL_POS).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Tablets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.type === WorkstationType.WAITER_TABLET).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Kitchen Displays</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.type === WorkstationType.KITCHEN_DISPLAY).length}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WorkstationManagement;
