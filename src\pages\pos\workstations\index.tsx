import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Monitor,
  Building2,
  Store,
  Printer,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Workstation, WorkstationRole } from '@/types/pos';
import { Screen } from '@/app-components/layout/screen';

// Mock data
const mockWorkstations: Workstation[] = [
  {
    workstation_id: '1',
    name: 'Main Counter POS',
    branch: '1',
    revenue_center: 'rc1',
    role: WorkstationRole.FULL_POS,
    ip_address: '*************',
    hostname: 'pos-main-01',
    linked_printer: 'printer1',
    supports_magnetic_card: true,
    supports_employee_id_login: true,
    is_online: true,
    language: 'en',
    branchData: { id: '1', code: 'BR001', name: 'Downtown Hotel', location: 'Nairobi CBD', timezone: 'Africa/Nairobi', currency: 'KES', language: 'en', tax_config: {}, is_active: true },
    revenueCenterData: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branch: '1' }
  },
  {
    workstation_id: '2',
    name: 'Waiter Tablet 1',
    branch: '1',
    revenue_center: 'rc1',
    role: WorkstationRole.WAITER_TABLET,
    ip_address: '*************',
    hostname: 'tablet-01',
    supports_magnetic_card: false,
    supports_employee_id_login: true,
    is_online: true,
    language: 'en',
    branchData: { id: '1', code: 'BR001', name: 'Downtown Hotel', location: 'Nairobi CBD', timezone: 'Africa/Nairobi', currency: 'KES', language: 'en', tax_config: {}, is_active: true },
    revenueCenterData: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branch: '1' }
  },
  {
    workstation_id: '3',
    name: 'Kitchen Display',
    branch: '1',
    revenue_center: 'rc1',
    role: WorkstationRole.KDS,
    ip_address: '*************',
    hostname: 'kds-01',
    supports_magnetic_card: false,
    supports_employee_id_login: false,
    is_online: false,
    language: 'en',
    branchData: { id: '1', code: 'BR001', name: 'Downtown Hotel', location: 'Nairobi CBD', timezone: 'Africa/Nairobi', currency: 'KES', language: 'en', tax_config: {}, is_active: true },
    revenueCenterData: { id: 'rc1', code: 'RC001', name: 'Main Restaurant', branch: '1' }
  }
];

const WorkstationManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [selectedType, setSelectedType] = useState('all');
  const [workstations] = useState<Workstation[]>(mockWorkstations);

  const columns: ColumnDef<Workstation>[] = [
    {
      accessorKey: 'name',
      header: 'Workstation Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Monitor className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.hostname}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.getValue('role') as WorkstationRole;
        const getRoleColor = (role: WorkstationRole) => {
          switch (role) {
            case WorkstationRole.FULL_POS: return 'bg-blue-100 text-blue-800';
            case WorkstationRole.WAITER_TABLET: return 'bg-green-100 text-green-800';
            case WorkstationRole.KDS: return 'bg-orange-100 text-orange-800';
            case WorkstationRole.ORDER_ONLY: return 'bg-purple-100 text-purple-800';
            case WorkstationRole.SELF_SERVICE: return 'bg-pink-100 text-pink-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };

        return (
          <Badge variant="outline" className={getRoleColor(role)}>
            {role}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'branchData',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.original.branchData;
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name}</div>
              <div className="text-sm text-muted-foreground">{branch?.code}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'revenueCenterData',
      header: 'Revenue Center',
      cell: ({ row }) => {
        const rc = row.original.revenueCenterData;
        return rc ? (
          <div className="flex items-center space-x-2">
            <Store className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{rc.name}</div>
              <div className="text-sm text-muted-foreground">{rc.code}</div>
            </div>
          </div>
        ) : (
          <span className="text-muted-foreground">Not assigned</span>
        );
      },
    },
    {
      accessorKey: 'ip_address',
      header: 'IP Address',
      cell: ({ row }) => {
        const ipAddress = row.getValue('ip_address') as string;
        const isOnline = row.original.is_online;
        return (
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="font-mono text-sm">{ipAddress}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'linked_printer',
      header: 'Printer',
      cell: ({ row }) => {
        const printerId = row.getValue('linked_printer') as string;
        return (
          <div className="flex items-center space-x-2">
            <Printer className="h-4 w-4 text-muted-foreground" />
            <span>{printerId ? '1' : '0'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'is_online',
      header: 'Status',
      cell: ({ row }) => {
        const isOnline = row.getValue('is_online') as boolean;
        return (
          <Badge variant={isOnline ? 'default' : 'secondary'}>
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const workstation = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Configuration
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Workstation
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Printer className="h-4 w-4 mr-2" />
                Manage Printers
              </DropdownMenuItem>
              <DropdownMenuItem>
                Reassign to Branch/RVC
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {workstation.is_online ? 'Take Offline' : 'Bring Online'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Workstation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredWorkstations = workstations.filter(ws => {
    const matchesSearch = ws.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.hostname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.ip_address?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBranch = selectedBranch === 'all' || ws.branch === selectedBranch;
    const matchesType = selectedType === 'all' || ws.role === selectedType;
    return matchesSearch && matchesBranch && matchesType;
  });

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workstation Management</h1>
          <p className="text-muted-foreground">
            Manage POS devices, terminals, and kitchen displays
          </p>
        </div>
        <Link to="/pos/workstations/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Workstation
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find workstations by name, IP address, or configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workstations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                <SelectItem value="1">Downtown Hotel</SelectItem>
                <SelectItem value="2">Airport Branch</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.values(WorkstationRole).map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Workstations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Workstations ({filteredWorkstations.length})</CardTitle>
          <CardDescription>
            POS devices and terminals across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredWorkstations}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Workstations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workstations.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {workstations.filter(ws => ws.is_online).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Full POS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.role === WorkstationRole.FULL_POS).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Tablets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.role === WorkstationRole.WAITER_TABLET).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Kitchen Displays</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstations.filter(ws => ws.role === WorkstationRole.KDS).length}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default WorkstationManagement;
