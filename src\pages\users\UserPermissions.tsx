import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Plus,
  Search,
  Shield,
  Edit,
  Trash2,
  Info,
  ArrowLeft,
  <PERSON>,
  Unlock,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Mock data for permissions
const mockPermissions = [
  {
    id: 1,
    name: "create_users",
    displayName: "Create Users",
    description: "Allows creating new user accounts in the system",
    category: "User Management",
    isActive: true,
    usageCount: 3,
  },
  {
    id: 2,
    name: "edit_users",
    displayName: "Edit Users",
    description: "Allows editing existing user accounts and their details",
    category: "User Management",
    isActive: true,
    usageCount: 5,
  },
  {
    id: 3,
    name: "delete_users",
    displayName: "Delete Users",
    description: "Allows permanently deleting user accounts from the system",
    category: "User Management",
    isActive: true,
    usageCount: 2,
  },
  {
    id: 4,
    name: "view_prospects",
    displayName: "View Prospects",
    description: "Allows viewing prospect information and contact details",
    category: "Prospect Management",
    isActive: true,
    usageCount: 25,
  },
  {
    id: 5,
    name: "create_prospects",
    displayName: "Create Prospects",
    description: "Allows adding new prospects to the system",
    category: "Prospect Management",
    isActive: true,
    usageCount: 20,
  },
  {
    id: 6,
    name: "edit_prospects",
    displayName: "Edit Prospects",
    description: "Allows modifying existing prospect information",
    category: "Prospect Management",
    isActive: true,
    usageCount: 18,
  },
  {
    id: 7,
    name: "view_campaigns",
    displayName: "View Campaigns",
    description: "Allows viewing marketing campaigns and their performance",
    category: "Marketing",
    isActive: true,
    usageCount: 12,
  },
  {
    id: 8,
    name: "create_campaigns",
    displayName: "Create Campaigns",
    description: "Allows creating new marketing campaigns",
    category: "Marketing",
    isActive: true,
    usageCount: 8,
  },
  {
    id: 9,
    name: "view_analytics",
    displayName: "View Analytics",
    description: "Allows accessing analytics and reporting dashboards",
    category: "Analytics",
    isActive: true,
    usageCount: 15,
  },
  {
    id: 10,
    name: "create_content",
    displayName: "Create Content",
    description: "Allows creating and publishing marketing content",
    category: "Content Management",
    isActive: false,
    usageCount: 5,
  },
];

const categories = [...new Set(mockPermissions.map(p => p.category))];

export default function UserPermissions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newPermission, setNewPermission] = useState({
    name: "",
    displayName: "",
    description: "",
    category: "",
  });
  const navigate = useNavigate();

  const filteredPermissions = mockPermissions.filter((permission) => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === "all" || permission.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const handleCreatePermission = () => {
    // Handle permission creation logic here
    console.log("Creating permission:", newPermission);
    setIsCreateModalOpen(false);
    setNewPermission({ name: "", displayName: "", description: "", category: "" });
  };

  const activePermissions = mockPermissions.filter(p => p.isActive).length;
  const inactivePermissions = mockPermissions.filter(p => !p.isActive).length;

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/admin/users">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Users
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">User Permissions</h1>
              <p className="text-muted-foreground">
                Manage system permissions and access controls
              </p>
            </div>
          </div>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Permission
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Permission</DialogTitle>
                <DialogDescription>
                  Create a new permission with detailed description.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="permission-name">Permission Name</Label>
                  <Input
                    id="permission-name"
                    placeholder="e.g., create_reports"
                    value={newPermission.name}
                    onChange={(e) => setNewPermission(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="permission-display-name">Display Name</Label>
                  <Input
                    id="permission-display-name"
                    placeholder="e.g., Create Reports"
                    value={newPermission.displayName}
                    onChange={(e) => setNewPermission(prev => ({ ...prev, displayName: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="permission-category">Category</Label>
                  <Select
                    value={newPermission.category}
                    onValueChange={(value) => setNewPermission(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="permission-description">Description</Label>
                  <Textarea
                    id="permission-description"
                    placeholder="Detailed description of what this permission allows..."
                    value={newPermission.description}
                    onChange={(e) => setNewPermission(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePermission}>Create Permission</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockPermissions.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Permissions</CardTitle>
              <Unlock className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{activePermissions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Permissions</CardTitle>
              <Lock className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{inactivePermissions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length}</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Permissions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions List</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Permission</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPermissions.map((permission) => (
                  <TableRow key={permission.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{permission.displayName}</div>
                        <div className="text-sm text-muted-foreground font-mono">
                          {permission.name}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{permission.category}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="max-w-xs truncate">{permission.description}</div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{permission.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={permission.isActive ? "default" : "secondary"}
                      >
                        {permission.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{permission.usageCount} users</Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Shield className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => navigate(`/admin/users/permissions/${permission.id}/edit`)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Permission
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => navigate(`/admin/users/permissions/${permission.id}/users`)}
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            View Users
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            {permission.isActive ? (
                              <>
                                <Lock className="mr-2 h-4 w-4" />
                                Deactivate
                              </>
                            ) : (
                              <>
                                <Unlock className="mr-2 h-4 w-4" />
                                Activate
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Permission
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}