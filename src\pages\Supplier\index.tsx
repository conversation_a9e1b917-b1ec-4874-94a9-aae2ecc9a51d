import { Screen } from "@/app-components/layout/screen";
import AddSupplier from "./modals/AddSupplier";
import { useState } from "react";
import { useGetSuppliersQuery } from "@/redux/slices/suppliers";
import { ColumnDef } from "@tanstack/react-table";
import { supplierType } from "@/types/suppliers";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";

const index = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const {
    data: suppliers,
    isLoading: loadingsuppliers,
    isFetching,
    isError,
    error,
  } = useGetSuppliersQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
  });

  const columns: ColumnDef<supplierType>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <Link
          to={`/suppliers/${info?.row?.original?.id}`}
          title="View Prospect"
        >
          <span className="font-medium underline capitalize text-blue-400">
            {info.getValue() as string}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "branch",
      header: "Branch",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "Hot"
              ? "bg-primary text-white px-3"
              : info?.getValue() === "Warm"
              ? "bg-yellow-400 text-black px-3"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
  ];
  ("");
  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold text-gray-800">Suppliers</h1>
        <div className="flex items-center gap-2">
          {/* <RefreshPermissionsButton /> */}
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add Supplier
          </Button>
        </div>
      </div>

      <DataTable<supplierType>
        data={suppliers?.data?.results || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search suppliers..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={suppliers?.data?.total_data || 0}
      />

      {isAddModalOpen && (
        <AddSupplier
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}
    </Screen>
  );
};

export default index;
