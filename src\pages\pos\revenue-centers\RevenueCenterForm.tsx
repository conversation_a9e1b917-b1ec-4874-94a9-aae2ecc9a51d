import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import { RevenueCenterFormData } from '@/types/pos';

interface RevenueCenterFormProps {
  mode: 'create' | 'edit';
}

// Mock data
const mockBranches = [
  { id: '1', name: 'Downtown Hotel', code: 'BR001' },
  { id: '2', name: 'Airport Branch', code: 'BR002' },
  { id: '3', name: 'Westlands Restaurant', code: 'BR003' }
];

const mockTaxClasses = [
  { id: 'tax-class-1', name: 'Food Tax (16%)', description: 'Standard food tax rate' },
  { id: 'tax-class-2', name: 'Beverage Tax (18%)', description: 'Alcoholic beverages tax' },
  { id: 'tax-class-3', name: 'Service Tax (12%)', description: 'Service charge tax' }
];

const salesCategoryOptions = [
  'food',
  'beverages',
  'snacks',
  'desserts',
  'alcohol',
  'non-alcohol',
  'room-service',
  'catering',
  'events'
];

const RevenueCenterForm: React.FC<RevenueCenterFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<RevenueCenterFormData>({
    defaultValues: {
      name: '',
      description: '',
      branchId: '',
      taxClassId: '',
      salesCategories: [],
      serviceChargeApplies: false,
      serviceChargeSettings: {
        percentage: 10,
        isInclusive: false,
        applicableCategories: []
      }
    },
  });

  const watchServiceCharge = form.watch('serviceChargeApplies');

  const onSubmit = async (data: RevenueCenterFormData) => {
    setIsLoading(true);
    try {
      console.log('Submitting revenue center data:', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/pos/revenue-centers');
    } catch (error) {
      console.error('Error saving revenue center:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateRvcCode = () => {
    const name = form.getValues('name');
    const branchId = form.getValues('branchId');
    const branch = mockBranches.find(b => b.id === branchId);
    
    if (name && branch) {
      const code = 'RC' + name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 100).toString().padStart(2, '0');
      return code;
    }
    return '';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/revenue-centers')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Revenue Center' : 'Edit Revenue Center'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new revenue center for a branch'
              : 'Update revenue center information and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the revenue center
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="branchId"
                rules={{ required: 'Parent branch is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parent Branch *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockBranches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name} ({branch.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: 'Revenue center name is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Revenue Center Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Main Restaurant, Bar & Lounge" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="space-y-2">
                  <Label>RVC Code</Label>
                  <div className="flex space-x-2">
                    <Input 
                      value={generateRvcCode()} 
                      readOnly 
                      className="bg-muted"
                      placeholder="Auto-generated"
                    />
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => {
                        form.trigger(['name', 'branchId']);
                      }}
                    >
                      Generate
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Code will be auto-generated based on name and branch
                  </p>
                </div>
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Optional description of the revenue center"
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Brief description of what this revenue center handles
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Tax and Sales Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Tax & Sales Configuration</CardTitle>
              <CardDescription>
                Configure tax classes and sales categories for this revenue center
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="taxClassId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Class</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tax class" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockTaxClasses.map((taxClass) => (
                          <SelectItem key={taxClass.id} value={taxClass.id}>
                            {taxClass.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Tax class that applies to items sold in this revenue center
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="salesCategories"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sales Categories</FormLabel>
                    <FormDescription>
                      Select the types of products/services sold in this revenue center
                    </FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                      {salesCategoryOptions.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={category}
                            checked={field.value.includes(category)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, category]);
                              } else {
                                field.onChange(field.value.filter(c => c !== category));
                              }
                            }}
                          />
                          <Label htmlFor={category} className="text-sm capitalize">
                            {category.replace('-', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Service Charge Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Service Charge Configuration</CardTitle>
              <CardDescription>
                Configure service charge settings for this revenue center
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="serviceChargeApplies"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Apply Service Charge
                      </FormLabel>
                      <FormDescription>
                        Enable service charge for items sold in this revenue center
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {watchServiceCharge && (
                <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                  <h4 className="font-medium">Service Charge Settings</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="serviceChargeSettings.percentage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Service Charge Percentage</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              max="100" 
                              step="0.1"
                              placeholder="10"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Percentage of service charge to apply
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="serviceChargeSettings.isInclusive"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-sm">
                              Inclusive Service Charge
                            </FormLabel>
                            <FormDescription className="text-xs">
                              Include service charge in item prices
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/revenue-centers')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Revenue Center' : 'Update Revenue Center'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RevenueCenterForm;
