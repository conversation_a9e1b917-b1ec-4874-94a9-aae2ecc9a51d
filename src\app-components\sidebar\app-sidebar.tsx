import React, { useState } from "react";
import {
  Sidebar,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";

import { Home, Cpu, BarChart2, Shield, Users, Settings, Grid, UserCheck, Clock } from "lucide-react";
import { NavUser } from "./nav-user";
import { NavMain } from "./nav-main";

const navData = {
  teams: [{ name: "GMC", logo: <PERSON><PERSON>, plan: "Franchise" }],
  navMain: [{ title: "Dashboard", url: "/", icon: Home, isActive: true }],
  navPerformance: [
    { title: "Marketer Performance", url: "/performance", icon: BarChart2 },
  ],
  navAdmin: [
    { 
      title: "User Management", 
      url: "/admin/users", 
      icon: Users,
      items: [
        { title: "User List", url: "/admin/users" },
        { title: "User Groups", url: "/admin/users/groups" },
        { title: "User Permissions", url: "/admin/users/permissions" },
        { title: "Permissions Matrix", url: "/admin/users/permissions-matrix" },
        { title: "Groups Permissions", url: "/admin/users/groups-permissions" },
        { title: "Groups Matrix", url: "/admin/users/groups-matrix" },
      ]
    },
    { 
      title: "Employee Management", 
      url: "/admin/employees", 
      icon: UserCheck,
      items: [
        { title: "Employee Profiles", url: "/admin/employees" },
        { title: "Shift Management", url: "/admin/shifts" },
      ]
    },
    { title: "System Settings", url: "/admin/settings", icon: Settings },
  ],
};


export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const [mainOpen, setMainOpen] = useState(true);
  const [performanceOpen, setPerformanceOpen] = useState(false);
  const [adminOpen, setAdminOpen] = useState(false);

  return (
    <Sidebar
      collapsible="icon"
      className="bg-sidebar text-sidebar-foreground"
      {...props}
    >
      <SidebarHeader className="border-b border-sidebar-border sticky top-0 z-10">
        <TeamSwitcher teams={navData.teams} />
      </SidebarHeader>

      <SidebarContent>
        <NavMain
          label="Main"
          items={navData.navMain}
          open={mainOpen}
          onToggle={() => setMainOpen(!mainOpen)}
        />
        <NavMain
          label="Performance"
          items={navData.navPerformance}
          open={performanceOpen}
          onToggle={() => setPerformanceOpen(!performanceOpen)}
        />
        <NavMain
          label="Administration"
          items={navData.navAdmin}
          open={adminOpen}
          onToggle={() => setAdminOpen(!adminOpen)}
        />
      </SidebarContent>

      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}

