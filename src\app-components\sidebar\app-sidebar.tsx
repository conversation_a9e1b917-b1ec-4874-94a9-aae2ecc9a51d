import React, { useState } from "react";
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";

import { Home, Cpu, BarChart2, Shield, Users, Settings, Grid, UserCheck, Clock, Building2, Store, Monitor, Calculator, Printer } from "lucide-react";
import { NavUser } from "./nav-user";
import { NavMain } from "./nav-main";

const navData = {
  teams: [{ name: "GMC", logo: Cpu, plan: "Franchise" }],
  navMain: [{ title: "Dashboard", url: "/", icon: Home, isActive: true }],
 
  navPOS: [
    { title: "POS Dashboard", url: "/pos", icon: Grid },
    {
      title: "Branch Management",
      url: "/pos/branches",
      icon: Building2,
      items: [
        { title: "All Branches", url: "/pos/branches" },
        { title: "Add New Branch", url: "/pos/branches/new" },
      ]
    },
    {
      title: "Revenue Centers",
      url: "/pos/revenue-centers",
      icon: Store,
      items: [
        { title: "All Revenue Centers", url: "/pos/revenue-centers" },
        { title: "Add Revenue Center", url: "/pos/revenue-centers/new" },
      ]
    },
    {
      title: "Workstations",
      url: "/pos/workstations",
      icon: Monitor,
      items: [
        { title: "All Workstations", url: "/pos/workstations" },
        { title: "Add Workstation", url: "/pos/workstations/new" },
      ]
    },
    { title: "Tax Configuration", url: "/pos/tax-configuration", icon: Calculator },
    {
      title: "Printer Management",
      url: "/pos/printers",
      icon: Printer,
      items: [
        { title: "All Printers", url: "/pos/printers" },
        { title: "Add Printer", url: "/pos/printers/new" },
      ]
    },
  ],
  navAdmin: [
    {
      title: "User Management",
      url: "/admin/users",
      icon: Users,
      items: [
        { title: "User List", url: "/admin/users" },
        { title: "User Groups", url: "/admin/users/groups" },
        { title: "User Permissions", url: "/admin/users/permissions" },
        { title: "Permissions Matrix", url: "/admin/users/permissions-matrix" },
        { title: "Groups Permissions", url: "/admin/users/groups-permissions" },
        { title: "Groups Matrix", url: "/admin/users/groups-matrix" },
      ]
    },
    {
      title: "Employee Management",
      url: "/admin/employees",
      icon: UserCheck,
      items: [
        { title: "Employee Profiles", url: "/admin/employees" },
        { title: "Shift Management", url: "/admin/shifts" },
      ]
    },
    { title: "System Settings", url: "/admin/settings", icon: Settings },
  ],
};


export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const [mainOpen, setMainOpen] = useState(true);
  const [posOpen, setPosOpen] = useState(false);
  const [adminOpen, setAdminOpen] = useState(false);

  return (
    <Sidebar
      collapsible="icon"
      className="bg-sidebar text-sidebar-foreground"
      {...props}
    >
      <SidebarHeader className="border-b border-sidebar-border sticky top-0 z-10">
        <TeamSwitcher teams={navData.teams} />
      </SidebarHeader>

      <SidebarContent>
        <NavMain
          label="Main"
          items={navData.navMain}
          open={mainOpen}
          onToggle={() => setMainOpen(!mainOpen)}
        />
        
        <NavMain
          label="POS Configuration"
          items={navData.navPOS}
          open={posOpen}
          onToggle={() => setPosOpen(!posOpen)}
        />
        <NavMain
          label="Administration"
          items={navData.navAdmin}
          open={adminOpen}
          onToggle={() => setAdminOpen(!adminOpen)}
        />
      </SidebarContent>

      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}

