// POS Configuration Types

export interface Branch {
  id: string;
  code: string;
  name: string;
  physicalLocation: string;
  address: string;
  timezone: string;
  currency: string;
  taxConfiguration: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  revenueCenters?: RevenueCenter[];
  workstations?: Workstation[];
}

export interface RevenueCenter {
  id: string;
  code: string;
  name: string;
  description?: string;
  branchId: string;
  branch?: Branch;
  taxClassId?: string;
  taxClass?: TaxClass;
  salesCategories: string[];
  workstations?: Workstation[];
  isActive: boolean;
  serviceChargeApplies: boolean;
  serviceChargeSettings?: ServiceChargeSettings;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceChargeSettings {
  percentage: number;
  isInclusive: boolean;
  applicableCategories: string[];
}

export interface Workstation {
  id: string;
  name: string;
  branchId: string;
  branch?: Branch;
  revenueCenterId?: string;
  revenueCenter?: RevenueCenter;
  type: WorkstationType;
  printerIds: string[];
  printers?: Printer[];
  ipAddress?: string;
  hostname?: string;
  touchscreenCompatible: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum WorkstationType {
  FULL_POS = 'Full POS (Orders + Payments)',
  ORDER_ONLY = 'Order Only',
  KITCHEN_DISPLAY = 'Kitchen Display',
  SELF_SERVICE_KIOSK = 'Self-Service Kiosk',
  WAITER_TABLET = 'Waiter Tablet'
}

export interface TaxClass {
  id: string;
  name: string;
  description: string;
  applicableRevenueCenters: string[];
  taxRates: TaxRate[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TaxRate {
  id: string;
  name: string;
  percentage: number;
  taxClassId: string;
  taxClass?: TaxClass;
  isInclusive: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Printer {
  id: string;
  name: string;
  branchId: string;
  branch?: Branch;
  revenueCenterId?: string;
  revenueCenter?: RevenueCenter;
  ipAddress?: string;
  deviceName?: string;
  interfaceType: PrinterInterfaceType;
  type: PrinterType;
  workstationIds: string[];
  workstations?: Workstation[];
  menuItemRouting: string[];
  categoryRouting: string[];
  backupPrinterId?: string;
  backupPrinter?: Printer;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum PrinterInterfaceType {
  LAN = 'LAN',
  WIFI = 'Wi-Fi',
  BLUETOOTH = 'Bluetooth'
}

export enum PrinterType {
  BILL = 'Bill',
  KITCHEN = 'Kitchen',
  RECEIPT = 'Receipt',
  BACKUP = 'Backup'
}

// Form Types
export interface BranchFormData {
  name: string;
  physicalLocation: string;
  address: string;
  timezone: string;
  currency: string;
  taxConfiguration: string[];
  isActive: boolean;
}

export interface RevenueCenterFormData {
  name: string;
  description?: string;
  branchId: string;
  taxClassId?: string;
  salesCategories: string[];
  serviceChargeApplies: boolean;
  serviceChargeSettings?: ServiceChargeSettings;
}

export interface WorkstationFormData {
  name: string;
  branchId: string;
  revenueCenterId?: string;
  type: WorkstationType;
  printerIds: string[];
  ipAddress?: string;
  hostname?: string;
  touchscreenCompatible: boolean;
}

export interface TaxClassFormData {
  name: string;
  description: string;
  applicableRevenueCenters: string[];
}

export interface TaxRateFormData {
  name: string;
  percentage: number;
  taxClassId: string;
  isInclusive: boolean;
}

export interface PrinterFormData {
  name: string;
  branchId: string;
  revenueCenterId?: string;
  ipAddress?: string;
  deviceName?: string;
  interfaceType: PrinterInterfaceType;
  type: PrinterType;
  workstationIds: string[];
  menuItemRouting: string[];
  categoryRouting: string[];
  backupPrinterId?: string;
}

// API Response Types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

// Filter and Search Types
export interface BranchFilters {
  search?: string;
  isActive?: boolean;
  timezone?: string;
  currency?: string;
}

export interface RevenueCenterFilters {
  search?: string;
  branchId?: string;
  isActive?: boolean;
  serviceChargeApplies?: boolean;
}

export interface WorkstationFilters {
  search?: string;
  branchId?: string;
  revenueCenterId?: string;
  type?: WorkstationType;
  isActive?: boolean;
}

export interface TaxClassFilters {
  search?: string;
  isActive?: boolean;
}

export interface PrinterFilters {
  search?: string;
  branchId?: string;
  revenueCenterId?: string;
  type?: PrinterType;
  interfaceType?: PrinterInterfaceType;
  isActive?: boolean;
}

// Constants
export const TIMEZONES = [
  'UTC',
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Australia/Sydney'
];

export const CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh' }
];
