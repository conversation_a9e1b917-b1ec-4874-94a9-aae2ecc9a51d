// POS Configuration Types - Matching Backend Models

export interface Branch {
  id: string;
  code: string; // auto-generated, unique
  name: string;
  location: string; // physical address
  timezone: string;
  currency: string; // e.g., KES, USD
  language: string; // default or selectable
  tax_config: any; // JSONField or ForeignKey → TaxClass
  is_active: boolean;
  revenueCenters?: RevenueCenter[];
  workstations?: Workstation[];
}

export interface RevenueCenter {
  id: string;
  name: string;
  code: string; // unique
  branch: string; // ForeignKey → Branch
  branchData?: Branch;
  workstations?: Workstation[];
}

export interface Workstation {
  workstation_id: string; // auto-generated, unique
  name: string;
  branch: string; // ForeignKey → Branch
  revenue_center: string; // ForeignKey → RevenueCenter
  role: WorkstationRole;
  ip_address: string; // GenericIPAddressField
  hostname: string;
  linked_printer?: string; // ForeignKey → Printer (nullable)
  supports_magnetic_card: boolean;
  supports_employee_id_login: boolean;
  is_online: boolean;
  language: string; // for future translation support
  branchData?: Branch;
  revenueCenterData?: RevenueCenter;
  printerData?: Printer;
}

export enum WorkstationRole {
  FULL_POS = 'Full POS',
  ORDER_ONLY = 'Order Only',
  KDS = 'KDS',
  SELF_SERVICE = 'Self-service',
  WAITER_TABLET = 'Waiter Tablet'
}

export interface TaxClass {
  id: string;
  name: string; // e.g., "Food Tax", "Bev Tax"
  description: string;
  default_rate: string; // ForeignKey → TaxRate
  taxRates?: TaxRate[];
}

export interface TaxRate {
  id: string;
  name: string; // e.g., "VAT 16%", "No VAT"
  taxclass: string; // FK TaxClass
  percentage: number; // e.g., 16.0
  is_active: boolean;
  taxClassData?: TaxClass;
}

export interface Printer {
  printer_id: string; // unique, auto-generated
  name: string;
  ip_address: string; // GenericIPAddressField
  linked_workstation?: string; // ForeignKey → Workstation (nullable)
  printer_type: PrinterType;
  interface_type: PrinterInterfaceType;
  location_note?: string; // TextField (optional)
  is_backup: boolean;
  receipt_template?: string; // ForeignKey → ReceiptTemplate (optional)
  print_language?: string; // optional
  workstationData?: Workstation;
  receiptTemplateData?: ReceiptTemplate;
}

export interface ReceiptTemplate {
  id: string;
  name: string;
  logo?: string; // ImageField (optional)
  footer_text: string;
  header_text: string;
  language: string;
  layout_config: any; // JSONField (e.g., fields to show/hide)
}

export enum PrinterInterfaceType {
  LAN = 'LAN',
  IP = 'IP',
  WIFI = 'Wi-Fi',
  BLUETOOTH = 'Bluetooth'
}

export enum PrinterType {
  RECEIPT = 'receipt',
  BILL = 'bill',
  KITCHEN = 'kitchen',
  BAR = 'bar'
}

// Employee Management Types - Matching Backend Models
export interface EmployeeRole {
  id: string;
  name: string; // unique
}

export interface Employee {
  id: string;
  employee_number?: string; // DateField (optional) - should be CharField
  password?: string; // DateField (optional) - should be CharField
  pin?: string; // DateField (optional) - should be CharField
  first_name: string;
  last_name: string;
  dob?: string; // DateField (optional)
  role: string; // ForeignKey → EmployeeRole
  branch: string; // ForeignKey → Branch
  revenue_center: string; // ForeignKey → RevenueCenter
  work_station: string; // ForeignKey → Workstation
  permit_number?: string; // optional
  permit_expiry?: string; // DateField (optional)
  hire_date: string; // DateField (default=now)
  employment_type: EmploymentType;
  hire_status: HireStatus;
  is_salaried: boolean;
  is_active: boolean; // soft-delete flag
  roleData?: EmployeeRole;
  branchData?: Branch;
  revenueCenterData?: RevenueCenter;
  workstationData?: Workstation;
}

export interface Shift {
  id: string;
  employee: string; // ForeignKey → Employee
  branch: string; // ForeignKey → Branch
  revenue_center: string; // ForeignKey → RevenueCenter
  workstation?: string; // ForeignKey → Workstation (optional)
  start_time: string; // DateTimeField
  end_time?: string; // DateTimeField (nullable)
  auto_ended: boolean; // default=False
  late_entry: boolean;
  early_exit: boolean;
  created_at: string; // DateTimeField (auto_now_add)
  employeeData?: Employee;
  branchData?: Branch;
  revenueCenterData?: RevenueCenter;
  workstationData?: Workstation;
}

export enum EmploymentType {
  PERMANENT = 'Permanent',
  PART_TIME = 'Part-time',
  TEMPORARY = 'Temporary'
}

export enum HireStatus {
  ACTIVE = 'Active',
  TERMINATED = 'Terminated',
  LEAVE = 'Leave'
}

// Form Types
export interface BranchFormData {
  name: string;
  location: string;
  timezone: string;
  currency: string;
  language: string;
  tax_config: any;
  is_active: boolean;
}

export interface RevenueCenterFormData {
  name: string;
  code: string;
  branch: string;
}

export interface WorkstationFormData {
  name: string;
  branch: string;
  revenue_center: string;
  role: WorkstationRole;
  ip_address: string;
  hostname: string;
  linked_printer?: string;
  supports_magnetic_card: boolean;
  supports_employee_id_login: boolean;
  language: string;
}

export interface TaxClassFormData {
  name: string;
  description: string;
  default_rate: string;
}

export interface TaxRateFormData {
  name: string;
  percentage: number;
  taxclass: string;
  is_active: boolean;
}

export interface PrinterFormData {
  name: string;
  ip_address: string;
  linked_workstation?: string;
  printer_type: PrinterType;
  interface_type: PrinterInterfaceType;
  location_note?: string;
  is_backup: boolean;
  receipt_template?: string;
  print_language?: string;
}

export interface EmployeeFormData {
  employee_number?: string;
  password?: string;
  pin?: string;
  first_name: string;
  last_name: string;
  dob?: string;
  role: string;
  branch: string;
  revenue_center: string;
  work_station: string;
  permit_number?: string;
  permit_expiry?: string;
  hire_date: string;
  employment_type: EmploymentType;
  hire_status: HireStatus;
  is_salaried: boolean;
  is_active: boolean;
}

// API Response Types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

// Filter and Search Types
export interface BranchFilters {
  search?: string;
  isActive?: boolean;
  timezone?: string;
  currency?: string;
}

export interface RevenueCenterFilters {
  search?: string;
  branchId?: string;
  isActive?: boolean;
  serviceChargeApplies?: boolean;
}

export interface WorkstationFilters {
  search?: string;
  branchId?: string;
  revenueCenterId?: string;
  role?: WorkstationRole;
  isActive?: boolean;
}

export interface TaxClassFilters {
  search?: string;
  isActive?: boolean;
}

export interface PrinterFilters {
  search?: string;
  branchId?: string;
  revenueCenterId?: string;
  type?: PrinterType;
  interfaceType?: PrinterInterfaceType;
  isActive?: boolean;
}

// Constants
export const TIMEZONES = [
  'UTC',
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Australia/Sydney'
];

export const CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh' }
];
