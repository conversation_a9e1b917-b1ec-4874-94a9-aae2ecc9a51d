import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Search,
  Save,
  RefreshCw,
  Info,
  Users,
  Shield,
  Check,
  X,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Mock data for groups
const mockGroups = [
  {
    id: 1,
    name: "Administrators",
    description: "Full system access and administrative privileges",
    memberCount: 3,
    color: "bg-red-100 text-red-800",
  },
  {
    id: 2,
    name: "Telemarketers",
    description: "Telemarketing staff with prospect management access",
    memberCount: 15,
    color: "bg-blue-100 text-blue-800",
  },
  {
    id: 3,
    name: "Marketers",
    description: "Marketing team with campaign and content management",
    memberCount: 8,
    color: "bg-green-100 text-green-800",
  },
  {
    id: 4,
    name: "Digital",
    description: "Digital marketing specialists and content creators",
    memberCount: 5,
    color: "bg-purple-100 text-purple-800",
  },
];

// Mock data for permissions
const mockPermissions = [
  {
    id: 1,
    name: "create_users",
    displayName: "Create Users",
    description: "Allows creating new user accounts in the system",
    category: "User Management",
  },
  {
    id: 2,
    name: "edit_users",
    displayName: "Edit Users",
    description: "Allows editing existing user accounts and their details",
    category: "User Management",
  },
  {
    id: 3,
    name: "delete_users",
    displayName: "Delete Users",
    description: "Allows permanently deleting user accounts from the system",
    category: "User Management",
  },
  {
    id: 4,
    name: "view_prospects",
    displayName: "View Prospects",
    description: "Allows viewing prospect information and contact details",
    category: "Prospect Management",
  },
  {
    id: 5,
    name: "create_prospects",
    displayName: "Create Prospects",
    description: "Allows adding new prospects to the system",
    category: "Prospect Management",
  },
  {
    id: 6,
    name: "edit_prospects",
    displayName: "Edit Prospects",
    description: "Allows modifying existing prospect information",
    category: "Prospect Management",
  },
  {
    id: 7,
    name: "view_campaigns",
    displayName: "View Campaigns",
    description: "Allows viewing marketing campaigns and their performance",
    category: "Marketing",
  },
  {
    id: 8,
    name: "create_campaigns",
    displayName: "Create Campaigns",
    description: "Allows creating new marketing campaigns",
    category: "Marketing",
  },
  {
    id: 9,
    name: "view_analytics",
    displayName: "View Analytics",
    description: "Allows accessing analytics and reporting dashboards",
    category: "Analytics",
  },
  {
    id: 10,
    name: "create_content",
    displayName: "Create Content",
    description: "Allows creating and publishing marketing content",
    category: "Content Management",
  },
];

// Mock group permissions matrix
const mockGroupPermissions = {
  1: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Administrators have all permissions
  2: [4, 5, 6], // Telemarketers have prospect permissions
  3: [7, 8, 9], // Marketers have campaign and analytics permissions
  4: [4, 7, 9, 10], // Digital specialists have view and content permissions
};

export default function UserGroupsMatrix() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [groupPermissions, setGroupPermissions] = useState(mockGroupPermissions);
  const [hasChanges, setHasChanges] = useState(false);

  const categories = [...new Set(mockPermissions.map(p => p.category))];

  const filteredPermissions = mockPermissions.filter((permission) => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === "all" || permission.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const handlePermissionChange = (groupId: number, permissionId: number, checked: boolean) => {
    setGroupPermissions(prev => {
      const groupPerms = prev[groupId] || [];
      const newPerms = checked
        ? [...groupPerms, permissionId]
        : groupPerms.filter(id => id !== permissionId);
      
      setHasChanges(true);
      return { ...prev, [groupId]: newPerms };
    });
  };

  const handleSelectAllGroups = (permissionId: number, checked: boolean) => {
    setGroupPermissions(prev => {
      const newPerms = { ...prev };
      mockGroups.forEach(group => {
        const groupPerms = newPerms[group.id] || [];
        newPerms[group.id] = checked
          ? [...new Set([...groupPerms, permissionId])]
          : groupPerms.filter(id => id !== permissionId);
      });
      setHasChanges(true);
      return newPerms;
    });
  };

  const handleSaveChanges = () => {
    // Handle save logic here
    console.log("Saving group permissions:", groupPermissions);
    setHasChanges(false);
  };

  const handleReset = () => {
    setGroupPermissions(mockGroupPermissions);
    setHasChanges(false);
  };

  const hasPermission = (groupId: number, permissionId: number) => {
    return groupPermissions[groupId]?.includes(permissionId) || false;
  };

  const getPermissionCount = (permissionId: number) => {
    return mockGroups.filter(group => hasPermission(group.id, permissionId)).length;
  };

  const getGroupPermissionCount = (groupId: number) => {
    return groupPermissions[groupId]?.length || 0;
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/admin/users">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Users
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">User Groups Matrix</h1>
              <p className="text-muted-foreground">
                Manage group permissions in a matrix format
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {hasChanges && (
              <Button variant="outline" onClick={handleReset}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset
              </Button>
            )}
            <Button onClick={handleSaveChanges} disabled={!hasChanges}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockGroups.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockPermissions.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Permission Categories</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockGroups.reduce((sum, group) => sum + group.memberCount, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Matrix Table */}
        <Card>
          <CardHeader>
            <CardTitle>Groups Permission Matrix</CardTitle>
          </CardHeader>
          <CardContent className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px] sticky left-0 bg-background">
                    Permission
                  </TableHead>
                  {mockGroups.map((group) => (
                    <TableHead key={group.id} className="text-center min-w-[140px]">
                      <div className="flex flex-col items-center space-y-1">
                        <div className="text-center">
                          <div className="font-medium">{group.name}</div>
                          <Badge className={`text-xs ${group.color}`}>
                            {group.memberCount} members
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {getGroupPermissionCount(group.id)} permissions
                        </div>
                      </div>
                    </TableHead>
                  ))}
                  <TableHead className="text-center min-w-[120px]">
                    <div className="flex flex-col items-center space-y-1">
                      <div className="text-xs font-medium">Select All</div>
                      <div className="text-xs text-muted-foreground">
                        Actions
                      </div>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPermissions.map((permission) => (
                  <TableRow key={permission.id}>
                    <TableCell className="sticky left-0 bg-background">
                      <div className="space-y-1">
                        <div className="font-medium">{permission.displayName}</div>
                        <div className="text-xs text-muted-foreground font-mono">
                          {permission.name}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {permission.category}
                        </Badge>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{permission.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                    {mockGroups.map((group) => (
                      <TableCell key={group.id} className="text-center">
                        <div className="flex items-center justify-center">
                          <Checkbox
                            checked={hasPermission(group.id, permission.id)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange(group.id, permission.id, checked as boolean)
                            }
                          />
                        </div>
                      </TableCell>
                    ))}
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <Checkbox
                          checked={getPermissionCount(permission.id) === mockGroups.length}
                          onCheckedChange={(checked) => 
                            handleSelectAllGroups(permission.id, checked as boolean)
                          }
                        />
                        <span className="text-xs text-muted-foreground">
                          {getPermissionCount(permission.id)}/{mockGroups.length}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {mockGroups.map((group) => (
            <Card key={group.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{group.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Members:</span>
                    <span className="font-medium">{group.memberCount}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Permissions:</span>
                    <span className="font-medium">{getGroupPermissionCount(group.id)}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {group.description}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Legend */}
        <Card>
          <CardHeader>
            <CardTitle>Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm">Permission granted</span>
              </div>
              <div className="flex items-center space-x-2">
                <X className="h-4 w-4 text-red-600" />
                <span className="text-sm">Permission denied</span>
              </div>
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Hover for permission description</span>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox />
                <span className="text-sm">Select all groups for this permission</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}