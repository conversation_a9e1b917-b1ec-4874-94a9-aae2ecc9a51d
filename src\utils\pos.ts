import { WorkstationType, PrinterType, PrinterInterfaceType } from '@/types/pos';

// Utility functions for POS configuration

export const generateCode = (prefix: string, name: string, length: number = 3): string => {
  if (!name) return '';
  const namePrefix = name.substring(0, length).toUpperCase();
  const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${namePrefix}${randomSuffix}`;
};

export const generateBranchCode = (name: string): string => {
  return generateCode('BR', name);
};

export const generateRevenueCenterCode = (name: string): string => {
  return generateCode('RC', name);
};

export const generateWorkstationCode = (name: string): string => {
  return generateCode('WS', name);
};

export const generatePrinterCode = (name: string): string => {
  return generateCode('PR', name);
};

export const getWorkstationTypeColor = (type: WorkstationType): string => {
  switch (type) {
    case WorkstationType.FULL_POS:
      return 'bg-blue-100 text-blue-800';
    case WorkstationType.WAITER_TABLET:
      return 'bg-green-100 text-green-800';
    case WorkstationType.KITCHEN_DISPLAY:
      return 'bg-orange-100 text-orange-800';
    case WorkstationType.ORDER_ONLY:
      return 'bg-purple-100 text-purple-800';
    case WorkstationType.SELF_SERVICE_KIOSK:
      return 'bg-pink-100 text-pink-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getPrinterTypeColor = (type: PrinterType): string => {
  switch (type) {
    case PrinterType.KITCHEN:
      return 'bg-orange-100 text-orange-800';
    case PrinterType.RECEIPT:
      return 'bg-blue-100 text-blue-800';
    case PrinterType.BILL:
      return 'bg-green-100 text-green-800';
    case PrinterType.BACKUP:
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getInterfaceTypeIcon = (type: PrinterInterfaceType): string => {
  switch (type) {
    case PrinterInterfaceType.LAN:
      return 'ethernet';
    case PrinterInterfaceType.WIFI:
      return 'wifi';
    case PrinterInterfaceType.BLUETOOTH:
      return 'bluetooth';
    default:
      return 'cable';
  }
};

export const validateIPAddress = (ip: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
};

export const validateHostname = (hostname: string): boolean => {
  const hostnameRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
  return hostnameRegex.test(hostname);
};

export const formatCurrency = (amount: number, currencyCode: string): string => {
  const currencyMap: Record<string, { symbol: string; locale: string }> = {
    USD: { symbol: '$', locale: 'en-US' },
    EUR: { symbol: '€', locale: 'en-EU' },
    GBP: { symbol: '£', locale: 'en-GB' },
    KES: { symbol: 'KSh', locale: 'en-KE' },
    UGX: { symbol: 'USh', locale: 'en-UG' },
    TZS: { symbol: 'TSh', locale: 'en-TZ' },
  };

  const currency = currencyMap[currencyCode] || { symbol: currencyCode, locale: 'en-US' };
  
  try {
    return new Intl.NumberFormat(currency.locale, {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  } catch {
    return `${currency.symbol} ${amount.toFixed(2)}`;
  }
};

export const calculateTax = (amount: number, taxRate: number, isInclusive: boolean): { taxAmount: number; totalAmount: number } => {
  if (isInclusive) {
    const taxAmount = (amount * taxRate) / (100 + taxRate);
    return {
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: amount
    };
  } else {
    const taxAmount = (amount * taxRate) / 100;
    return {
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: Math.round((amount + taxAmount) * 100) / 100
    };
  }
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

export const getStatusColor = (isActive: boolean): string => {
  return isActive ? 'text-green-600' : 'text-red-600';
};

export const getStatusText = (isActive: boolean, activeText: string = 'Active', inactiveText: string = 'Inactive'): string => {
  return isActive ? activeText : inactiveText;
};

// Date formatting utilities
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

export const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};

export const formatTime = (dateString: string): string => {
  return new Date(dateString).toLocaleTimeString();
};

// Search and filter utilities
export const searchItems = <T extends Record<string, any>>(
  items: T[],
  searchTerm: string,
  searchFields: (keyof T)[]
): T[] => {
  if (!searchTerm) return items;
  
  const lowercaseSearch = searchTerm.toLowerCase();
  
  return items.filter(item =>
    searchFields.some(field => {
      const value = item[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(lowercaseSearch);
      }
      if (typeof value === 'number') {
        return value.toString().includes(lowercaseSearch);
      }
      return false;
    })
  );
};

export const filterByStatus = <T extends { isActive: boolean }>(
  items: T[],
  status: 'all' | 'active' | 'inactive'
): T[] => {
  if (status === 'all') return items;
  return items.filter(item => 
    status === 'active' ? item.isActive : !item.isActive
  );
};

export const sortItems = <T extends Record<string, any>>(
  items: T[],
  sortField: keyof T,
  sortDirection: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
};
