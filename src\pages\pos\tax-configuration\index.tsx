import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Calculator,
  Percent,
  Archive,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TaxClass, TaxRate } from '@/types/pos';

// Mock data
const mockTaxClasses: TaxClass[] = [
  {
    id: 'tax-class-1',
    name: 'Food Tax',
    description: 'Standard tax rate for food items',
    default_rate: 'rate-1',
    taxRates: [
      {
        id: 'rate-1',
        name: 'VAT 16%',
        percentage: 16,
        taxclass: 'tax-class-1',
        is_active: true
      }
    ]
  },
  {
    id: 'tax-class-2',
    name: 'Beverage Tax',
    description: 'Tax rate for alcoholic beverages',
    default_rate: 'rate-2',
    taxRates: [
      {
        id: 'rate-2',
        name: 'Alcohol Tax 18%',
        percentage: 18,
        taxclass: 'tax-class-2',
        is_active: true
      }
    ]
  },
  {
    id: 'tax-class-3',
    name: 'Service Tax',
    description: 'Tax rate for service charges',
    default_rate: 'rate-3',
    taxRates: [
      {
        id: 'rate-3',
        name: 'Service VAT 12%',
        percentage: 12,
        taxclass: 'tax-class-3',
        is_active: true
      }
    ]
  }
];

const TaxConfiguration: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [taxClasses] = useState<TaxClass[]>(mockTaxClasses);

  const taxClassColumns: ColumnDef<TaxClass>[] = [
    {
      accessorKey: 'name',
      header: 'Tax Class Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Calculator className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.description}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'taxRates',
      header: 'Tax Rates',
      cell: ({ row }) => {
        const taxRates = row.getValue('taxRates') as TaxRate[];
        return (
          <div className="flex flex-wrap gap-1">
            {taxRates.map((rate) => (
              <Badge key={rate.id} variant="outline" className="flex items-center space-x-1">
                <Percent className="h-3 w-3" />
                <span>{rate.percentage}%</span>
                {rate.isInclusive && <span className="text-xs">(Inc)</span>}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: 'default_rate',
      header: 'Default Rate',
      cell: ({ row }) => {
        const defaultRate = row.getValue('default_rate') as string;
        return (
          <Badge variant="outline">
            {defaultRate}
          </Badge>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant="default">Active</Badge>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const taxClass = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/tax-configuration/classes/${taxClass.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/tax-configuration/classes/${taxClass.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Tax Class
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Plus className="h-4 w-4 mr-2" />
                Add Tax Rate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                Preview Usage
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Archive className="h-4 w-4 mr-2" />
                Archive
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const allTaxRates = taxClasses.flatMap(tc => 
    tc.taxRates.map(rate => ({ ...rate, taxClassName: tc.name }))
  );

  const taxRateColumns: ColumnDef<TaxRate & { taxClassName: string }>[] = [
    {
      accessorKey: 'name',
      header: 'Rate Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Percent className="h-4 w-4 text-muted-foreground" />
          <div className="font-medium">{row.getValue('name')}</div>
        </div>
      ),
    },
    {
      accessorKey: 'taxClassName',
      header: 'Tax Class',
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue('taxClassName')}</Badge>
      ),
    },
    {
      accessorKey: 'percentage',
      header: 'Rate',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <span className="font-medium">{row.getValue('percentage')}%</span>
          {row.original.isInclusive && (
            <Badge variant="secondary" className="text-xs">Inclusive</Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const rate = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit Rate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Usage
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Delete Rate
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredTaxClasses = taxClasses.filter(tc =>
    tc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tc.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tax Configuration</h1>
          <p className="text-muted-foreground">
            Manage tax classes and rates for products and services
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link to="/pos/tax-configuration/classes/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Class
            </Button>
          </Link>
          <Link to="/pos/tax-configuration/rates/new">
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Rate
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find tax classes and rates by name or description
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tax classes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tax Configuration Tabs */}
      <Tabs defaultValue="classes" className="space-y-6">
        <TabsList>
          <TabsTrigger value="classes">Tax Classes</TabsTrigger>
          <TabsTrigger value="rates">Tax Rates</TabsTrigger>
          <TabsTrigger value="preview">Usage Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="classes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Classes ({filteredTaxClasses.length})</CardTitle>
              <CardDescription>
                Categories of tax that apply to different types of products or services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={filteredTaxClasses}
                columns={taxClassColumns}
                enablePagination={true}
                enableSorting={true}
                enableColumnFilters={true}
                enableSelectColumn={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Rates ({allTaxRates.length})</CardTitle>
              <CardDescription>
                Specific percentage rates assigned to tax classes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={allTaxRates}
                columns={taxRateColumns}
                enablePagination={true}
                enableSorting={true}
                enableColumnFilters={true}
                enableSelectColumn={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Usage Preview</CardTitle>
              <CardDescription>
                See which menu items, products, or revenue centers use each tax class
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {taxClasses.map((taxClass) => (
                  <div key={taxClass.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{taxClass.name}</h4>
                      <Badge variant="default">Active</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{taxClass.description}</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Default Rate:</span>
                        <div className="mt-1">
                          {taxClass.default_rate}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Menu Items:</span>
                        <div className="mt-1">0 items (Not implemented)</div>
                      </div>
                      <div>
                        <span className="font-medium">Products:</span>
                        <div className="mt-1">0 products (Not implemented)</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Tax Classes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{taxClasses.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Classes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {taxClasses.filter(tc => tc.isActive).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Tax Rates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allTaxRates.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {allTaxRates.length > 0 
                ? (allTaxRates.reduce((sum, rate) => sum + rate.percentage, 0) / allTaxRates.length).toFixed(1)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default TaxConfiguration;
