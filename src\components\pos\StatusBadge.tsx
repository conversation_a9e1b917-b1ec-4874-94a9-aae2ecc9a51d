import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  isActive: boolean;
  activeText?: string;
  inactiveText?: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  isActive,
  activeText = 'Active',
  inactiveText = 'Inactive',
  variant,
  className
}) => {
  const badgeVariant = variant || (isActive ? 'default' : 'secondary');
  
  return (
    <Badge 
      variant={badgeVariant} 
      className={cn(
        isActive ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-gray-100 text-gray-800 hover:bg-gray-100',
        className
      )}
    >
      {isActive ? activeText : inactiveText}
    </Badge>
  );
};

export default StatusBadge;
