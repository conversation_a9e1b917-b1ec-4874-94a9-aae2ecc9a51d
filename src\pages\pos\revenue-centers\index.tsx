import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Store,
  Building2,
  Calculator,
  Monitor
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RevenueCenter, Branch } from '@/types/pos';

// Mock data
const mockBranches: Branch[] = [
  { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: 'Nairobi CBD', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
  { id: '2', code: 'BR002', name: 'Airport Branch', physicalLocation: 'JKIA Terminal 1', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' }
];

const mockRevenueCenters: RevenueCenter[] = [
  {
    id: '1',
    code: 'RC001',
    name: 'Main Restaurant',
    description: 'Primary dining area with full service',
    branchId: '1',
    branch: mockBranches[0],
    taxClassId: 'tax-class-1',
    salesCategories: ['food', 'beverages'],
    isActive: true,
    serviceChargeApplies: true,
    serviceChargeSettings: { percentage: 10, isInclusive: false, applicableCategories: ['food'] },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    code: 'RC002',
    name: 'Bar & Lounge',
    description: 'Cocktail bar and lounge area',
    branchId: '1',
    branch: mockBranches[0],
    taxClassId: 'tax-class-2',
    salesCategories: ['beverages', 'snacks'],
    isActive: true,
    serviceChargeApplies: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '3',
    code: 'RC003',
    name: 'Room Service',
    description: 'In-room dining service',
    branchId: '2',
    branch: mockBranches[1],
    taxClassId: 'tax-class-1',
    salesCategories: ['food', 'beverages'],
    isActive: true,
    serviceChargeApplies: true,
    serviceChargeSettings: { percentage: 15, isInclusive: true, applicableCategories: ['food', 'beverages'] },
    createdAt: '2024-01-16T10:00:00Z',
    updatedAt: '2024-01-16T10:00:00Z'
  }
];

const RevenueCenterManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [revenueCenters] = useState<RevenueCenter[]>(mockRevenueCenters);

  const columns: ColumnDef<RevenueCenter>[] = [
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('code')}</div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Revenue Center Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Store className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.description}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.original.branch;
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name}</div>
              <div className="text-sm text-muted-foreground">{branch?.code}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'salesCategories',
      header: 'Sales Categories',
      cell: ({ row }) => {
        const categories = row.getValue('salesCategories') as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {categories.map((category, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {category}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: 'serviceChargeApplies',
      header: 'Service Charge',
      cell: ({ row }) => {
        const applies = row.getValue('serviceChargeApplies') as boolean;
        const settings = row.original.serviceChargeSettings;
        return (
          <div>
            <Badge variant={applies ? 'default' : 'secondary'}>
              {applies ? 'Yes' : 'No'}
            </Badge>
            {applies && settings && (
              <div className="text-xs text-muted-foreground mt-1">
                {settings.percentage}% {settings.isInclusive ? '(Inclusive)' : '(Exclusive)'}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const revenueCenter = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Revenue Center
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Monitor className="h-4 w-4 mr-2" />
                Manage Workstations
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calculator className="h-4 w-4 mr-2" />
                Tax Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {revenueCenter.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredRevenueCenters = revenueCenters.filter(rc => {
    const matchesSearch = rc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rc.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rc.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBranch = selectedBranch === 'all' || rc.branchId === selectedBranch;
    return matchesSearch && matchesBranch;
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Revenue Center Management</h1>
          <p className="text-muted-foreground">
            Manage business units within branches that generate income
          </p>
        </div>
        <Link to="/pos/revenue-centers/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Revenue Center
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find revenue centers by name, code, or branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search revenue centers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {mockBranches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Centers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Centers ({filteredRevenueCenters.length})</CardTitle>
          <CardDescription>
            Business units configured across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredRevenueCenters}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Revenue Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{revenueCenters.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {revenueCenters.filter(rc => rc.isActive).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">With Service Charge</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {revenueCenters.filter(rc => rc.serviceChargeApplies).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Branches Covered</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(revenueCenters.map(rc => rc.branchId)).size}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RevenueCenterManagement;
