import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Store,
  Building2,
  Calculator,
  Monitor
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RevenueCenter, Branch } from '@/types/pos';

// Mock data
const mockBranches: Branch[] = [
  { id: '1', code: 'BR001', name: 'Downtown Hotel', location: 'Nairobi CBD', timezone: 'Africa/Nairobi', currency: 'KES', language: 'en', tax_config: {}, is_active: true },
  { id: '2', code: 'BR002', name: 'Airport Branch', location: 'JKIA Terminal 1', timezone: 'Africa/Nairobi', currency: 'KES', language: 'en', tax_config: {}, is_active: true }
];

const mockRevenueCenters: RevenueCenter[] = [
  {
    id: '1',
    code: 'RC001',
    name: 'Main Restaurant',
    branch: '1',
    branchData: mockBranches[0]
  },
  {
    id: '2',
    code: 'RC002',
    name: 'Bar & Lounge',
    branch: '1',
    branchData: mockBranches[0]
  },
  {
    id: '3',
    code: 'RC003',
    name: 'Room Service',
    branch: '2',
    branchData: mockBranches[1]
  }
];

const RevenueCenterManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [revenueCenters] = useState<RevenueCenter[]>(mockRevenueCenters);

  const columns: ColumnDef<RevenueCenter>[] = [
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('code')}</div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Revenue Center Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Store className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">Revenue Center</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'branchData',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.original.branchData;
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name}</div>
              <div className="text-sm text-muted-foreground">{branch?.code}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue('code')}</Badge>
      ),
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant="default">Active</Badge>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const revenueCenter = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Revenue Center
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Monitor className="h-4 w-4 mr-2" />
                Manage Workstations
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calculator className="h-4 w-4 mr-2" />
                Tax Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Deactivate
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredRevenueCenters = revenueCenters.filter(rc => {
    const matchesSearch = rc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rc.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBranch = selectedBranch === 'all' || rc.branch === selectedBranch;
    return matchesSearch && matchesBranch;
  });

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Revenue Center Management</h1>
          <p className="text-muted-foreground">
            Manage business units within branches that generate income
          </p>
        </div>
        <Link to="/pos/revenue-centers/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Revenue Center
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find revenue centers by name, code, or branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search revenue centers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {mockBranches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Centers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Centers ({filteredRevenueCenters.length})</CardTitle>
          <CardDescription>
            Business units configured across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredRevenueCenters}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Revenue Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{revenueCenters.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {revenueCenters.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Revenue Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {revenueCenters.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Branches Covered</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(revenueCenters.map(rc => rc.branch)).size}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default RevenueCenterManagement;
