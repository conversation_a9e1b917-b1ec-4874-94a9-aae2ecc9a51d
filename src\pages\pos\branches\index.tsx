import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Building2,
  MapPin,
  Clock,
  DollarSign
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Branch } from '@/types/pos';

// Mock data for demonstration
const mockBranches: Branch[] = [
  {
    id: '1',
    code: 'BR001',
    name: 'Downtown Hotel',
    location: '123 Kenyatta Avenue, Nairobi',
    timezone: 'Africa/Nairobi',
    currency: 'KES',
    language: 'en',
    tax_config: { default_tax_class: 'tax-class-1' },
    is_active: true
  },
  {
    id: '2',
    code: 'BR002',
    name: 'Airport Branch',
    location: 'Jomo Kenyatta International Airport',
    timezone: 'Africa/Nairobi',
    currency: 'KES',
    language: 'en',
    tax_config: { default_tax_class: 'tax-class-1' },
    is_active: true
  },
  {
    id: '3',
    code: 'BR003',
    name: 'Westlands Restaurant',
    location: '456 Waiyaki Way, Westlands',
    timezone: 'Africa/Nairobi',
    currency: 'KES',
    language: 'en',
    tax_config: { default_tax_class: 'tax-class-1' },
    is_active: false
  }
];

const BranchManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [branches] = useState<Branch[]>(mockBranches);

  const columns: ColumnDef<Branch>[] = [
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('code')}</div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Branch Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue('name')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span>{row.getValue('location')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'timezone',
      header: 'Timezone',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.getValue('timezone')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'currency',
      header: 'Currency',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue('currency')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const branch = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/branches/${branch.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/branches/${branch.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Branch
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {branch.is_active ? 'Deactivate' : 'Activate'} Branch
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredBranches = branches.filter(branch =>
    branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Branch Management</h1>
          <p className="text-muted-foreground">
            Manage hotel or restaurant chain branches with localization and tax settings
          </p>
        </div>
        <Link to="/pos/branches/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add New Branch
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find branches by name, code, or location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search branches..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Branches Table */}
      <Card>
        <CardHeader>
          <CardTitle>Branches ({filteredBranches.length})</CardTitle>
          <CardDescription>
            A list of all branches in your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredBranches}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={true}
            enableSelectColumn={false}
          />
        </CardContent>
      </Card>
      </div>
    </Screen>
  );
};

export default BranchManagement;
