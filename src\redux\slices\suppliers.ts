import { apiSlice } from "../apiSlice";

export const supplierApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSuppliers: builder.query({
      query: (params) => ({
        url: "/suppliers",
        method: "GET",
        params: params,
      }),
    }),

    addSuppliers: builder.mutation({
      query: (payload) => ({
        url: "/suppliers",
        method: "POST",
        body: payload,
      }),
    }),
  }),
});

export const { useGetSuppliersQuery, useAddSuppliersMutation } =
  supplierApiSlice;
